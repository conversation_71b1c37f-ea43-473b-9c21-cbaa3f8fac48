<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <!-- super pom -->
  <parent>
    <groupId>com.ctrip.dcs.order</groupId>
    <artifactId>vbk-order-rest-service</artifactId>
    <version>1.0.0</version>
  </parent>

  <!-- api module -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.ctrip.dcs.order</groupId>
  <artifactId>vbk-order-rest-service-api</artifactId>
  <version>1.0.0</version>
  <name>vbk-order-rest-service-api</name>
  <url>http://www.ctrip.com</url>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
  </properties>

  <!-- dependencies -->
  <dependencies>
    <dependency>
      <groupId>com.ctriposs.baiji</groupId>
      <artifactId>baiji-rpc-client</artifactId>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</project>
