namespace java com.ctrip.igt
namespace csharp IGT.API

/*服务请求头*/
class RequestHeader {
    /*【废弃字段】语言【zh-cn：中文（简体），en-us：英文(美国)】 使用locale代替*/
    string language,
    /*站点域名【hk.trip.com，www.trip.com，m.ctrip.com】*/
    string host,
    /*语言【en-英国，hk-香港】*/
    string languageCode,
    /*站点本地语言【zh-cn：中文（简体），en-us：英文(美国)】*/
    string locale,
    /*币种【cny-人民币,hkd-港币,eur-欧元,usd-美元,jpy-日元,krw-韩币,twd-台币,sgd-新币,gbp-英镑】*/
    string currency,
    /*渠道ID*/
    int channelId,
    /*产品形态 【17-接机，18-送机，55-点对点，23-按天包车，66-线路包车，88-定制包车】*/
    int patternType,
    /*产品形态组【1718-接送机，55-点对点，23-按天包车，66-线路包车，88-定制包车】*/
    int patternGroup,
    /*业务形态【32-国内，33-海外】*/
    int businessType,
    /*业务来源【online,h5,app】*/
    string severFrom,
    /*客户端ID【online-客户端IP地址，无线-框架生成的客户端ID】*/
    string cid,
    /*ubt相关参数*/
    CommonUBTDTO ubt,
    /*分销信息*/
    CommonAllianceDTO union,
    /*【用户明文UID】*/
    string uid,
    /*客户端IP地址*/
    string ip,
    /*online登录态*/
    string ticket,
    /*用户定位信息*/
    CommonGPSDTO gps,
    /*手机系统【ios，android】*/
    string os,
    /*技术模式【h5,hybrid,crn,mini(小程序)*/
    string mode,
    /*无线版本号(如7月31号发布7.6.2版本，【online：0，H5：0.0731,APP:7062.0731】)*/
    decimal wirelessVersion,
    /*鉴权标示*/
    string token,
    /*全局TraceId*/
    string globalTraceId;
    /*风控所需token*/
    string rmsToken;
    /*系统版本号【5.0】*/
    string osVersion;
    //客户端机器设备ID【硬件标识】
    string did;
    /*应用id(携程APP-9999，IBUApp-37，去哪儿app-xx，小程序应用id-xx)*/
    string appid;
    /*小程序特殊参数*/
    CommonMiniProgramDTO miniProgram;
}

/*服务响应结果*/
class ResponseResult {
    /*是否成功*/
    bool success,
    /*响应Code*/
    string returnCode,
    /*响应信息*/
    string returnMessage,
}

/*rest服务请求头*/
class RestRequestHeader {
    /*【废弃字段】语言【zh-cn：中文（简体），en-us：英文(美国)】*/
    string lang,
    /*站点域名【hk.trip.com，www.trip.com，m.ctrip.com】*/
    string host,
    /*语言【en-英国，hk-香港】*/
    string language,
    /*站点本地语言【zh-cn：中文（简体），en-us：英文(美国)】*/
    string locale,
    /*币种【cny-人民币,hkd-港币,eur-欧元,usd-美元,jpy-日元,krw-韩币,twd-台币,sgd-新币,gbp-英镑】*/
    string cury,
    /*渠道ID*/
    int channelid,
    /*产品形态 【17-接机，18-送机，55-点对点，23-按天包车，66-线路包车，88-定制包车】*/
    int pttype,
    /*产品形态组【1718-接送机，55-点对点，23-按天包车，66-线路包车，88-定制包车】*/
    int ptgroup,
    /*业务形态【32-国内，33-海外】*/
    int biztype,
    /*业务来源【online,h5,app】*/
    string sf,
    /*客户端ID【online-客户端IP地址，无线-框架生成的客户端ID】*/
    string cid,
    /*ubt相关参数*/
    CommonUBTDTO ubt,
    /*分销信息*/
    CommonAllianceDTO union,
    /*【用户明文UID】*/
    string uid,
    /*客户端IP地址*/
    string ip,
    /*online登录态*/
    string ticket,
    /*用户定位信息*/
    CommonGPSDTO gps,
    /*手机系统【ios，android】*/
    string os,
    /*技术模式【h5,hybrid,crn,mini(小程序)*/
    string mode,
    /*无线版本号(如7月31号发布7.6.2版本，【online：0，H5：0.0731,APP:7062.0731】)*/
    decimal wlver,
    /*鉴权标示*/
    string token,
    /*全局TraceId*/
    string globalTraceId;
    /*风控所需token*/
    string rmstoken;
    /*系统版本号【5.0】*/
    string osver;
    /*登录签名【携程：登录态auth，去哪儿：登录签名串】*/
    string sign;
    /*客户端机器设备ID【硬件标识】*/
    string did;
    /*应用id(携程APP-9999，IBUApp-37，去哪儿app-xx，小程序应用id-xx)*/
    string appid;
    /*小程序特殊参数*/
    CommonMiniProgramDTO miniprgm;
    /*来源【c-携程,q-去哪】*/
    string sc;
}

/*rest服务响应结果*/
class RestResponseResult {
    /*20X-成功，50X-失败*/
    string rcode,
    /*响应信息*/
    string rmsg,
    /*响应扩展信息*/
    map<string,string> rext,
    /*环境信息*/
    string env
}

class CommonUBTDTO {
    /* abtest版本，例如：M:4,160608_ind_phnum:A; */
    map<string,string> abtest;
    /* 页面id */
    string pageid;
    /*pageview标识 */
    string pvid,
    /*session 标识*/
    string sid,
    /*网站用户身份标识*/
    string vid
}

class CommonAllianceDTO {
    /*主渠道ID*/
    string aid,
    /*副渠道ID*/
    string sid,
    /*分销用户id*/
    string ouid,
    /*分销订单业绩采集信息*/
    string mktinfo
}

class CommonGPSDTO {
    /*定位经纬度*/
    string lat,
    /*定位经纬度*/
    string lng,
    /*定位城市id*/
    int cid,
    /*定位城市名称*/
    string cnm,
    /*坐标系(WGS84/GCJ02/BD09)*/
    string coord,
    /*定位q端城市id*/
    string qcid
}

class CommonMiniProgramDTO {
    /*单个微信小程序上用户的唯一标识*/
    string openid,
    /*多个微信产品端使用相同的微信用户ID*/
    string unionid,
}

/*分页器*/
class PaginatorDTO {
    /*页码*/
    int pageNo;
    /*分页条目数*/
    int pageSize;
}

/*分页结果*/
class PaginationDTO {
    /*页码*/
    int pageNo;
    /*分页条数*/
    int pageSize
    /*总页数*/
    int totalPages;
    /*总记录数*/
    int totalSize;
}
